"""
Alibaba Cloud API client for floor plan generation.
"""

import requests
import json
import time
import base64
import hashlib
import hmac
from typing import Optional, Dict, Any, Union
from datetime import datetime
from urllib.parse import quote
import logging
from pathlib import Path
from PIL import Image
import io

from .config import APISettings, GenerationSettings


class AlibabaAPIError(Exception):
    """Custom exception for Alibaba API errors."""
    pass


class AlibabaAPIClient:
    """Client for interacting with Alibaba's image generation API."""
    
    def __init__(self, api_settings: Optional[APISettings] = None, 
                 generation_settings: Optional[GenerationSettings] = None):
        """Initialize the API client."""
        self.api_settings = api_settings or APISettings()
        self.generation_settings = generation_settings or GenerationSettings()
        
        # Set up logging
        self._setup_logging()
        
        # Validate settings
        self._validate_settings()
    
    def _setup_logging(self):
        """Set up logging configuration."""
        log_dir = Path(self.generation_settings.log_file).parent
        log_dir.mkdir(parents=True, exist_ok=True)
        
        logging.basicConfig(
            level=getattr(logging, self.generation_settings.log_level),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.generation_settings.log_file),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def _validate_settings(self):
        """Validate API settings."""
        if not self.api_settings.alibaba_api_key:
            raise ValueError("Alibaba API key is required")
        if not self.api_settings.alibaba_api_secret:
            raise ValueError("Alibaba API secret is required")
    
    def _generate_signature(self, method: str, url: str, params: Dict[str, Any], 
                          timestamp: str, nonce: str) -> str:
        """Generate API signature for authentication."""
        # Sort parameters
        sorted_params = sorted(params.items())
        query_string = "&".join([f"{k}={quote(str(v))}" for k, v in sorted_params])
        
        # Create string to sign
        string_to_sign = f"{method}\n{url}\n{query_string}\n{timestamp}\n{nonce}"
        
        # Generate signature
        signature = hmac.new(
            self.api_settings.alibaba_api_secret.encode('utf-8'),
            string_to_sign.encode('utf-8'),
            hashlib.sha256
        ).digest()
        
        return base64.b64encode(signature).decode('utf-8')
    
    def _prepare_headers(self, content_type: str = "application/json") -> Dict[str, str]:
        """Prepare request headers with authentication."""
        timestamp = str(int(time.time() * 1000))
        nonce = str(int(time.time() * 1000000))
        
        headers = {
            "Content-Type": content_type,
            "Authorization": f"Bearer {self.api_settings.alibaba_api_key}",
            "X-DashScope-Async": "enable",  # Enable async processing for large requests
        }
        
        return headers
    
    def _make_request(self, method: str, url: str, data: Optional[Dict[str, Any]] = None,
                     files: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Make HTTP request to the API with retry logic."""
        headers = self._prepare_headers()
        
        for attempt in range(self.generation_settings.max_retries):
            try:
                self.logger.info(f"Making {method} request to {url} (attempt {attempt + 1})")
                
                if method.upper() == "POST":
                    if files:
                        # For file uploads, don't set Content-Type header (requests will set it)
                        headers.pop("Content-Type", None)
                        response = requests.post(
                            url, 
                            data=data, 
                            files=files, 
                            headers=headers,
                            timeout=self.generation_settings.timeout_seconds
                        )
                    else:
                        response = requests.post(
                            url, 
                            json=data, 
                            headers=headers,
                            timeout=self.generation_settings.timeout_seconds
                        )
                else:
                    response = requests.get(
                        url, 
                        params=data, 
                        headers=headers,
                        timeout=self.generation_settings.timeout_seconds
                    )
                
                # Check response status
                if response.status_code == 200:
                    result = response.json()
                    self.logger.info(f"Request successful: {result.get('request_id', 'unknown')}")
                    return result
                elif response.status_code == 429:  # Rate limit
                    wait_time = 2 ** attempt
                    self.logger.warning(f"Rate limited, waiting {wait_time} seconds")
                    time.sleep(wait_time)
                    continue
                else:
                    error_msg = f"API request failed with status {response.status_code}: {response.text}"
                    self.logger.error(error_msg)
                    if attempt == self.generation_settings.max_retries - 1:
                        raise AlibabaAPIError(error_msg)
                    
            except requests.exceptions.Timeout:
                self.logger.warning(f"Request timeout (attempt {attempt + 1})")
                if attempt == self.generation_settings.max_retries - 1:
                    raise AlibabaAPIError("Request timed out after all retries")
                    
            except requests.exceptions.RequestException as e:
                self.logger.error(f"Request error: {str(e)}")
                if attempt == self.generation_settings.max_retries - 1:
                    raise AlibabaAPIError(f"Request failed: {str(e)}")
            
            # Wait before retry
            if attempt < self.generation_settings.max_retries - 1:
                wait_time = 2 ** attempt
                self.logger.info(f"Waiting {wait_time} seconds before retry")
                time.sleep(wait_time)
        
        raise AlibabaAPIError("All retry attempts failed")
    
    def text_to_image(self, prompt: str, negative_prompt: Optional[str] = None,
                     image_size: Optional[str] = None, steps: Optional[int] = None,
                     guidance_scale: Optional[float] = None) -> Dict[str, Any]:
        """Generate image from text prompt."""
        self.logger.info(f"Starting text-to-image generation with prompt: {prompt[:100]}...")
        
        # Prepare request data
        data = {
            "model": self.api_settings.alibaba_model_name,
            "input": {
                "prompt": prompt,
                "negative_prompt": negative_prompt or "",
            },
            "parameters": {
                "size": image_size or self.generation_settings.default_image_size,
                "n": 1,
                "steps": steps or self.generation_settings.default_steps,
                "scale": guidance_scale or self.generation_settings.default_guidance_scale,
            }
        }
        
        return self._make_request("POST", self.api_settings.alibaba_endpoint, data)
    
    def image_to_image(self, prompt: str, input_image_path: str, 
                      negative_prompt: Optional[str] = None,
                      strength: float = 0.8, image_size: Optional[str] = None,
                      steps: Optional[int] = None, guidance_scale: Optional[float] = None) -> Dict[str, Any]:
        """Generate image from text prompt and input image."""
        self.logger.info(f"Starting image-to-image generation with prompt: {prompt[:100]}...")
        
        # Validate input image
        input_path = Path(input_image_path)
        if not input_path.exists():
            raise FileNotFoundError(f"Input image not found: {input_image_path}")
        
        # Prepare image data
        with open(input_path, 'rb') as f:
            image_data = base64.b64encode(f.read()).decode('utf-8')
        
        # Prepare request data
        data = {
            "model": self.api_settings.alibaba_model_name,
            "input": {
                "prompt": prompt,
                "negative_prompt": negative_prompt or "",
                "image": image_data,
            },
            "parameters": {
                "size": image_size or self.generation_settings.default_image_size,
                "n": 1,
                "steps": steps or self.generation_settings.default_steps,
                "scale": guidance_scale or self.generation_settings.default_guidance_scale,
                "strength": strength,
            }
        }
        
        return self._make_request("POST", self.api_settings.alibaba_endpoint, data)
    
    def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """Get the status of an async generation task."""
        status_url = f"{self.api_settings.alibaba_endpoint}/tasks/{task_id}"
        return self._make_request("GET", status_url)
    
    def wait_for_completion(self, task_id: str, max_wait_time: int = 300) -> Dict[str, Any]:
        """Wait for an async task to complete."""
        self.logger.info(f"Waiting for task completion: {task_id}")
        
        start_time = time.time()
        while time.time() - start_time < max_wait_time:
            status = self.get_task_status(task_id)
            
            if status.get("output", {}).get("task_status") == "SUCCEEDED":
                self.logger.info(f"Task completed successfully: {task_id}")
                return status
            elif status.get("output", {}).get("task_status") == "FAILED":
                error_msg = f"Task failed: {task_id}"
                self.logger.error(error_msg)
                raise AlibabaAPIError(error_msg)
            
            # Wait before checking again
            time.sleep(5)
        
        raise AlibabaAPIError(f"Task timed out: {task_id}")
    
    def download_image(self, image_url: str, output_path: str) -> str:
        """Download generated image from URL."""
        self.logger.info(f"Downloading image to: {output_path}")
        
        response = requests.get(image_url, timeout=self.generation_settings.timeout_seconds)
        response.raise_for_status()
        
        # Create output directory if it doesn't exist
        output_path = Path(output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Save image
        with open(output_path, 'wb') as f:
            f.write(response.content)
        
        self.logger.info(f"Image saved successfully: {output_path}")
        return str(output_path)
    
    def validate_image(self, image_path: str) -> bool:
        """Validate that the image file is valid."""
        try:
            with Image.open(image_path) as img:
                img.verify()
            return True
        except Exception as e:
            self.logger.error(f"Image validation failed: {e}")
            return False
