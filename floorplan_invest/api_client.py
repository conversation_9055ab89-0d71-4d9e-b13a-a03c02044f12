"""
LangChain-based Alibaba Cloud API client for floor plan generation.

This module provides a LangChain-compatible wrapper for Alibaba's DashScope API,
following LangChain's design patterns and abstractions.
"""

import asyncio
import base64
import hashlib
import hmac
import json
import time
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
from urllib.parse import quote

import httpx
from langchain_core.callbacks import CallbackManagerForLLMRun
from langchain_core.language_models.base import BaseLanguageModel
from langchain_core.outputs import Generation, LLMResult
from pydantic import BaseModel, Field, model_validator
from langchain_core.utils import get_from_dict_or_env
from PIL import Image
from tenacity import (
    retry,
    retry_if_exception_type,
    stop_after_attempt,
    wait_exponential,
)

from .config import APISettings, GenerationSettings


class AlibabaAPIError(Exception):
    """Custom exception for Alibaba API errors."""
    pass


class ImageGenerationRequest(BaseModel):
    """Request model for image generation."""
    prompt: str = Field(..., description="Text prompt for image generation")
    negative_prompt: Optional[str] = Field(None, description="Negative prompt")
    image_size: str = Field("1024x1024", description="Image size")
    steps: int = Field(20, ge=1, le=100, description="Generation steps")
    guidance_scale: float = Field(7.5, ge=1.0, le=20.0, description="Guidance scale")
    strength: Optional[float] = Field(None, ge=0.0, le=1.0, description="Image-to-image strength")
    input_image: Optional[str] = Field(None, description="Base64 encoded input image")


class ImageGenerationResponse(BaseModel):
    """Response model for image generation."""
    success: bool
    task_id: Optional[str] = None
    image_url: Optional[str] = None
    error: Optional[str] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)


class AlibabaDashScopeImageGenerator(BaseLanguageModel):
    """LangChain-compatible Alibaba DashScope image generation client."""

    # Configuration fields following LangChain patterns
    alibaba_api_key: str = Field(..., description="Alibaba Cloud API key")
    alibaba_api_secret: str = Field(..., description="Alibaba Cloud API secret")
    alibaba_endpoint: str = Field(
        default="https://dashscope.aliyuncs.com/api/v1/services/aigc/text2image/image-synthesis",
        description="API endpoint"
    )
    alibaba_model_name: str = Field(default="wanx-v1", description="Model name")

    # Generation settings
    default_image_size: str = Field(default="1024x1024", description="Default image size")
    default_steps: int = Field(default=20, description="Default generation steps")
    default_guidance_scale: float = Field(default=7.5, description="Default guidance scale")
    max_retries: int = Field(default=3, description="Maximum retries")
    timeout_seconds: int = Field(default=60, description="Request timeout")

    # LangChain required fields
    model_name: str = Field(default="alibaba-dashscope", description="Model identifier")

    class Config:
        """Pydantic configuration."""
        extra = "forbid"

    @model_validator(mode='before')
    @classmethod
    def validate_environment(cls, values: Dict) -> Dict:
        """Validate environment variables."""
        if isinstance(values, dict):
            values["alibaba_api_key"] = get_from_dict_or_env(
                values, "alibaba_api_key", "ALIBABA_API_KEY"
            )
            values["alibaba_api_secret"] = get_from_dict_or_env(
                values, "alibaba_api_secret", "ALIBABA_API_SECRET"
            )
        return values

    @property
    def _llm_type(self) -> str:
        """Return type of language model."""
        return "alibaba-dashscope-image"

    def _generate_signature(self, method: str, url: str, params: Dict[str, Any],
                          timestamp: str, nonce: str) -> str:
        """Generate API signature for authentication."""
        # Sort parameters
        sorted_params = sorted(params.items())
        query_string = "&".join([f"{k}={quote(str(v))}" for k, v in sorted_params])

        # Create string to sign
        string_to_sign = f"{method}\n{url}\n{query_string}\n{timestamp}\n{nonce}"

        # Generate signature
        signature = hmac.new(
            self.alibaba_api_secret.encode('utf-8'),
            string_to_sign.encode('utf-8'),
            hashlib.sha256
        ).digest()

        return base64.b64encode(signature).decode('utf-8')

    def _prepare_headers(self, content_type: str = "application/json") -> Dict[str, str]:
        """Prepare request headers with authentication."""
        timestamp = str(int(time.time() * 1000))
        nonce = str(int(time.time() * 1000000))

        headers = {
            "Content-Type": content_type,
            "Authorization": f"Bearer {self.alibaba_api_key}",
            "X-DashScope-Async": "enable",  # Enable async processing for large requests
        }

        return headers

    @retry(
        retry=retry_if_exception_type((httpx.HTTPError, httpx.TimeoutException)),
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        reraise=True,
    )
    async def _make_async_request(
        self,
        method: str,
        url: str,
        data: Optional[Dict[str, Any]] = None,
        files: Optional[Dict[str, Any]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None
    ) -> Dict[str, Any]:
        """Make async HTTP request with LangChain-style retry logic."""
        headers = self._prepare_headers()

        if run_manager:
            run_manager.on_text(f"Making {method} request to {url}")

        async with httpx.AsyncClient(timeout=self.timeout_seconds) as client:
            try:
                if method.upper() == "POST":
                    if files:
                        # For file uploads, don't set Content-Type header
                        headers.pop("Content-Type", None)
                        response = await client.post(url, data=data, files=files, headers=headers)
                    else:
                        response = await client.post(url, json=data, headers=headers)
                else:
                    response = await client.get(url, params=data, headers=headers)

                response.raise_for_status()
                result = response.json()

                if run_manager:
                    run_manager.on_text(f"Request successful: {result.get('request_id', 'unknown')}")

                return result

            except httpx.HTTPStatusError as e:
                error_msg = f"API request failed with status {e.response.status_code}: {e.response.text}"
                if run_manager:
                    run_manager.on_text(f"Request failed: {error_msg}")
                raise AlibabaAPIError(error_msg)
            except httpx.TimeoutException:
                error_msg = "Request timed out"
                if run_manager:
                    run_manager.on_text(f"Request timeout: {error_msg}")
                raise AlibabaAPIError(error_msg)

    def _make_request(
        self,
        method: str,
        url: str,
        data: Optional[Dict[str, Any]] = None,
        files: Optional[Dict[str, Any]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None
    ) -> Dict[str, Any]:
        """Synchronous wrapper for async request method."""
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

        return loop.run_until_complete(
            self._make_async_request(method, url, data, files, run_manager)
        )

    def _generate(
        self,
        prompts: List[str],
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> LLMResult:
        """Generate images from prompts (LangChain interface)."""
        generations = []

        for prompt in prompts:
            try:
                response = self.text_to_image(
                    prompt=prompt,
                    run_manager=run_manager,
                    **kwargs
                )

                generation = Generation(
                    text=response.get("image_url", ""),
                    generation_info=response
                )
                generations.append([generation])

            except Exception as e:
                generation = Generation(
                    text="",
                    generation_info={"error": str(e)}
                )
                generations.append([generation])

        return LLMResult(generations=generations)

    def text_to_image(
        self,
        prompt: str,
        negative_prompt: Optional[str] = None,
        image_size: Optional[str] = None,
        steps: Optional[int] = None,
        guidance_scale: Optional[float] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None
    ) -> Dict[str, Any]:
        """Generate image from text prompt."""
        if run_manager:
            run_manager.on_text(f"Starting text-to-image generation with prompt: {prompt[:100]}...")

        # Prepare request data
        data = {
            "model": self.alibaba_model_name,
            "input": {
                "prompt": prompt,
                "negative_prompt": negative_prompt or "",
            },
            "parameters": {
                "size": image_size or self.default_image_size,
                "n": 1,
                "steps": steps or self.default_steps,
                "scale": guidance_scale or self.default_guidance_scale,
            }
        }

        return self._make_request("POST", self.alibaba_endpoint, data, run_manager=run_manager)

    def image_to_image(
        self,
        prompt: str,
        input_image_path: str,
        negative_prompt: Optional[str] = None,
        strength: float = 0.8,
        image_size: Optional[str] = None,
        steps: Optional[int] = None,
        guidance_scale: Optional[float] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None
    ) -> Dict[str, Any]:
        """Generate image from text prompt and input image."""
        if run_manager:
            run_manager.on_text(f"Starting image-to-image generation with prompt: {prompt[:100]}...")

        # Validate input image
        input_path = Path(input_image_path)
        if not input_path.exists():
            raise FileNotFoundError(f"Input image not found: {input_image_path}")

        # Prepare image data
        with open(input_path, 'rb') as f:
            image_data = base64.b64encode(f.read()).decode('utf-8')

        # Prepare request data
        data = {
            "model": self.alibaba_model_name,
            "input": {
                "prompt": prompt,
                "negative_prompt": negative_prompt or "",
                "image": image_data,
            },
            "parameters": {
                "size": image_size or self.default_image_size,
                "n": 1,
                "steps": steps or self.default_steps,
                "scale": guidance_scale or self.default_guidance_scale,
                "strength": strength,
            }
        }

        return self._make_request("POST", self.alibaba_endpoint, data, run_manager=run_manager)

    def get_task_status(
        self,
        task_id: str,
        run_manager: Optional[CallbackManagerForLLMRun] = None
    ) -> Dict[str, Any]:
        """Get the status of an async generation task."""
        status_url = f"{self.alibaba_endpoint}/tasks/{task_id}"
        return self._make_request("GET", status_url, run_manager=run_manager)

    def wait_for_completion(
        self,
        task_id: str,
        max_wait_time: int = 300,
        run_manager: Optional[CallbackManagerForLLMRun] = None
    ) -> Dict[str, Any]:
        """Wait for an async task to complete."""
        if run_manager:
            run_manager.on_text(f"Waiting for task completion: {task_id}")

        start_time = time.time()
        while time.time() - start_time < max_wait_time:
            status = self.get_task_status(task_id, run_manager)

            if status.get("output", {}).get("task_status") == "SUCCEEDED":
                if run_manager:
                    run_manager.on_text(f"Task completed successfully: {task_id}")
                return status
            elif status.get("output", {}).get("task_status") == "FAILED":
                error_msg = f"Task failed: {task_id}"
                if run_manager:
                    run_manager.on_text(f"Task failed: {error_msg}")
                raise AlibabaAPIError(error_msg)

            # Wait before checking again
            time.sleep(5)

        raise AlibabaAPIError(f"Task timed out: {task_id}")

    @retry(
        retry=retry_if_exception_type(httpx.HTTPError),
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=2, max=8),
    )
    async def _download_image_async(self, image_url: str, output_path: str) -> str:
        """Download image asynchronously with retry logic."""
        async with httpx.AsyncClient(timeout=self.timeout_seconds) as client:
            response = await client.get(image_url)
            response.raise_for_status()

            # Create output directory if it doesn't exist
            output_path = Path(output_path)
            output_path.parent.mkdir(parents=True, exist_ok=True)

            # Save image
            with open(output_path, 'wb') as f:
                f.write(response.content)

            return str(output_path)

    def download_image(
        self,
        image_url: str,
        output_path: str,
        run_manager: Optional[CallbackManagerForLLMRun] = None
    ) -> str:
        """Download generated image from URL."""
        if run_manager:
            run_manager.on_text(f"Downloading image to: {output_path}")

        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

        result = loop.run_until_complete(self._download_image_async(image_url, output_path))

        if run_manager:
            run_manager.on_text(f"Image saved successfully: {result}")

        return result

    def validate_image(self, image_path: str) -> bool:
        """Validate that the image file is valid."""
        try:
            with Image.open(image_path) as img:
                img.verify()
            return True
        except Exception as e:
            # Use LangChain-style logging if available
            if hasattr(self, 'callbacks') and self.callbacks:
                for callback in self.callbacks:
                    if hasattr(callback, 'on_text'):
                        callback.on_text(f"Image validation failed: {e}")
            return False


# Backward compatibility wrapper
class AlibabaAPIClient:
    """Backward compatibility wrapper for the LangChain-based client."""

    def __init__(self, api_settings: Optional[APISettings] = None,
                 generation_settings: Optional[GenerationSettings] = None):
        """Initialize the API client with backward compatibility."""
        self.api_settings = api_settings or APISettings()
        self.generation_settings = generation_settings or GenerationSettings()

        # Create the LangChain-based client
        self._client = AlibabaDashScopeImageGenerator(
            alibaba_api_key=self.api_settings.alibaba_api_key,
            alibaba_api_secret=self.api_settings.alibaba_api_secret,
            alibaba_endpoint=self.api_settings.alibaba_endpoint,
            alibaba_model_name=self.api_settings.alibaba_model_name,
            default_image_size=self.generation_settings.default_image_size,
            default_steps=self.generation_settings.default_steps,
            default_guidance_scale=self.generation_settings.default_guidance_scale,
            max_retries=self.generation_settings.max_retries,
            timeout_seconds=self.generation_settings.timeout_seconds,
        )

        # Set up logging for backward compatibility
        import logging
        log_dir = Path(self.generation_settings.log_file).parent
        log_dir.mkdir(parents=True, exist_ok=True)

        logging.basicConfig(
            level=getattr(logging, self.generation_settings.log_level),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.generation_settings.log_file),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def text_to_image(self, prompt: str, negative_prompt: Optional[str] = None,
                     image_size: Optional[str] = None, steps: Optional[int] = None,
                     guidance_scale: Optional[float] = None) -> Dict[str, Any]:
        """Generate image from text prompt (backward compatibility)."""
        return self._client.text_to_image(
            prompt=prompt,
            negative_prompt=negative_prompt,
            image_size=image_size,
            steps=steps,
            guidance_scale=guidance_scale
        )

    def image_to_image(self, prompt: str, input_image_path: str,
                      negative_prompt: Optional[str] = None,
                      strength: float = 0.8, image_size: Optional[str] = None,
                      steps: Optional[int] = None, guidance_scale: Optional[float] = None) -> Dict[str, Any]:
        """Generate image from text prompt and input image (backward compatibility)."""
        return self._client.image_to_image(
            prompt=prompt,
            input_image_path=input_image_path,
            negative_prompt=negative_prompt,
            strength=strength,
            image_size=image_size,
            steps=steps,
            guidance_scale=guidance_scale
        )

    def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """Get the status of an async generation task (backward compatibility)."""
        return self._client.get_task_status(task_id)

    def wait_for_completion(self, task_id: str, max_wait_time: int = 300) -> Dict[str, Any]:
        """Wait for an async task to complete (backward compatibility)."""
        return self._client.wait_for_completion(task_id, max_wait_time)

    def download_image(self, image_url: str, output_path: str) -> str:
        """Download generated image from URL (backward compatibility)."""
        return self._client.download_image(image_url, output_path)

    def validate_image(self, image_path: str) -> bool:
        """Validate that the image file is valid (backward compatibility)."""
        return self._client.validate_image(image_path)
