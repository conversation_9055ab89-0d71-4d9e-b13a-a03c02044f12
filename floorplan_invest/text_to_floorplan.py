#!/usr/bin/env python3
"""
Text-to-image floor plan generation script.

This script generates floor plans from text prompts using Alibaba's AI models.
It supports various configuration options and provides comprehensive testing capabilities.
"""

import click
import json
import time
from pathlib import Path
from typing import Optional, Dict, Any, List
from datetime import datetime
import logging

from .config import (
    get_config, list_available_configs, get_api_settings, get_generation_settings,
    FloorPlanConfig, APISettings, GenerationSettings
)
from .api_client import AlibabaAPIC<PERSON>, AlibabaAPIError
from .prompt_engineering import FloorPlanPromptBuilder, PromptVariationGenerator, create_prompt_for_config


class FloorPlanGenerator:
    """Main class for text-to-image floor plan generation."""
    
    def __init__(self, api_settings: Optional[APISettings] = None,
                 generation_settings: Optional[GenerationSettings] = None):
        """Initialize the floor plan generator."""
        self.api_settings = api_settings or get_api_settings()
        self.generation_settings = generation_settings or get_generation_settings()
        self.api_client = AlibabaAPIClient(self.api_settings, self.generation_settings)
        self.prompt_builder = FloorPlanPromptBuilder()
        
        # Set up output directories
        self.output_dir = Path(self.generation_settings.output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Set up logging
        self.logger = logging.getLogger(__name__)
    
    def generate_single_floorplan(self, config: FloorPlanConfig, 
                                 output_filename: Optional[str] = None,
                                 custom_prompt: Optional[str] = None) -> Dict[str, Any]:
        """Generate a single floor plan from configuration."""
        self.logger.info(f"Starting generation for: {config.name}")
        
        # Build prompt
        if custom_prompt:
            prompt = custom_prompt
        else:
            prompt = self.prompt_builder.build_text_to_image_prompt(config)
        
        negative_prompt = self.prompt_builder.build_negative_prompt()
        
        self.logger.info(f"Generated prompt: {prompt[:200]}...")
        
        try:
            # Make API request
            response = self.api_client.text_to_image(
                prompt=prompt,
                negative_prompt=negative_prompt,
                image_size=self.generation_settings.default_image_size,
                steps=self.generation_settings.default_steps,
                guidance_scale=self.generation_settings.default_guidance_scale
            )
            
            # Handle async response
            if "task_id" in response.get("output", {}):
                task_id = response["output"]["task_id"]
                self.logger.info(f"Async task created: {task_id}")
                
                # Wait for completion
                final_response = self.api_client.wait_for_completion(task_id)
                image_url = final_response["output"]["results"][0]["url"]
            else:
                # Synchronous response
                image_url = response["output"]["results"][0]["url"]
            
            # Download image
            if not output_filename:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                safe_name = config.name.lower().replace(" ", "_").replace("-", "_")
                output_filename = f"{safe_name}_{timestamp}.png"
            
            output_path = self.output_dir / output_filename
            downloaded_path = self.api_client.download_image(image_url, str(output_path))
            
            # Validate image
            if not self.api_client.validate_image(downloaded_path):
                raise AlibabaAPIError("Generated image failed validation")
            
            result = {
                "success": True,
                "config_name": config.name,
                "prompt": prompt,
                "negative_prompt": negative_prompt,
                "output_path": downloaded_path,
                "image_url": image_url,
                "generation_time": datetime.now().isoformat(),
                "settings": {
                    "image_size": self.generation_settings.default_image_size,
                    "steps": self.generation_settings.default_steps,
                    "guidance_scale": self.generation_settings.default_guidance_scale
                }
            }
            
            self.logger.info(f"Successfully generated floor plan: {downloaded_path}")
            return result
            
        except Exception as e:
            error_result = {
                "success": False,
                "config_name": config.name,
                "prompt": prompt,
                "error": str(e),
                "generation_time": datetime.now().isoformat()
            }
            self.logger.error(f"Generation failed: {str(e)}")
            return error_result
    
    def generate_variations(self, config: FloorPlanConfig, 
                          num_variations: int = 3) -> List[Dict[str, Any]]:
        """Generate multiple variations of a floor plan."""
        self.logger.info(f"Generating {num_variations} variations for: {config.name}")
        
        variation_generator = PromptVariationGenerator(self.prompt_builder)
        variations = variation_generator.generate_variations(config, num_variations)
        
        results = []
        for i, variation in enumerate(variations):
            self.logger.info(f"Generating variation {i+1}/{num_variations}: {variation['description']}")
            
            result = self.generate_single_floorplan(
                config=config,
                output_filename=f"{variation['name']}.png",
                custom_prompt=variation['prompt']
            )
            
            result["variation_info"] = {
                "variation_index": i,
                "description": variation['description']
            }
            
            results.append(result)
            
            # Small delay between generations to avoid rate limiting
            if i < len(variations) - 1:
                time.sleep(2)
        
        return results
    
    def batch_generate(self, config_names: List[str], 
                      generate_variations: bool = False) -> Dict[str, Any]:
        """Generate floor plans for multiple configurations."""
        self.logger.info(f"Starting batch generation for {len(config_names)} configurations")
        
        batch_results = {
            "batch_id": datetime.now().strftime("%Y%m%d_%H%M%S"),
            "total_configs": len(config_names),
            "results": [],
            "summary": {"successful": 0, "failed": 0}
        }
        
        for config_name in config_names:
            try:
                config = get_config(config_name)
                self.logger.info(f"Processing configuration: {config_name}")
                
                if generate_variations:
                    config_results = self.generate_variations(config)
                else:
                    config_results = [self.generate_single_floorplan(config)]
                
                batch_results["results"].extend(config_results)
                
                # Update summary
                for result in config_results:
                    if result["success"]:
                        batch_results["summary"]["successful"] += 1
                    else:
                        batch_results["summary"]["failed"] += 1
                
            except Exception as e:
                error_result = {
                    "success": False,
                    "config_name": config_name,
                    "error": f"Configuration error: {str(e)}",
                    "generation_time": datetime.now().isoformat()
                }
                batch_results["results"].append(error_result)
                batch_results["summary"]["failed"] += 1
                self.logger.error(f"Failed to process {config_name}: {str(e)}")
        
        # Save batch results
        results_file = self.output_dir / f"batch_results_{batch_results['batch_id']}.json"
        with open(results_file, 'w') as f:
            json.dump(batch_results, f, indent=2)
        
        self.logger.info(f"Batch generation completed. Results saved to: {results_file}")
        return batch_results


@click.command()
@click.option('--config', '-c', help='Floor plan configuration name')
@click.option('--list-configs', is_flag=True, help='List available configurations')
@click.option('--variations', '-v', default=1, help='Number of variations to generate')
@click.option('--batch', '-b', help='Comma-separated list of config names for batch generation')
@click.option('--output', '-o', help='Output filename (for single generation)')
@click.option('--custom-prompt', help='Custom prompt instead of generated one')
@click.option('--dry-run', is_flag=True, help='Show prompts without generating images')
def main(config: Optional[str], list_configs: bool, variations: int, 
         batch: Optional[str], output: Optional[str], custom_prompt: Optional[str],
         dry_run: bool):
    """
    Generate architectural floor plans from text descriptions using Alibaba's AI models.
    
    Examples:
        text-to-floorplan --list-configs
        text-to-floorplan --config 1br_apartment
        text-to-floorplan --config 2br_apartment --variations 3
        text-to-floorplan --batch "1br_apartment,2br_apartment,3br_house"
    """
    
    if list_configs:
        available_configs = list_available_configs()
        click.echo("Available floor plan configurations:")
        for config_name in available_configs:
            try:
                cfg = get_config(config_name)
                click.echo(f"  {config_name}: {cfg.name} ({cfg.total_area_sqm}m²)")
            except Exception as e:
                click.echo(f"  {config_name}: Error loading config - {e}")
        return
    
    if not config and not batch:
        click.echo("Error: Must specify either --config or --batch")
        click.echo("Use --list-configs to see available configurations")
        return
    
    try:
        if dry_run:
            # Show prompts without generating (no API client needed)
            if config:
                cfg = get_config(config)
                builder = FloorPlanPromptBuilder()

                if custom_prompt:
                    prompt = custom_prompt
                else:
                    prompt = builder.build_text_to_image_prompt(cfg)
                negative_prompt = builder.build_negative_prompt()

                click.echo(f"\nConfiguration: {cfg.name}")
                click.echo(f"Prompt: {prompt}")
                click.echo(f"Negative Prompt: {negative_prompt}")
            return

        generator = FloorPlanGenerator()
        
        if batch:
            # Batch generation
            config_names = [name.strip() for name in batch.split(',')]
            results = generator.batch_generate(config_names, generate_variations=(variations > 1))
            
            click.echo(f"\nBatch generation completed:")
            click.echo(f"  Successful: {results['summary']['successful']}")
            click.echo(f"  Failed: {results['summary']['failed']}")
            
        else:
            # Single configuration
            cfg = get_config(config)
            
            if variations > 1:
                results = generator.generate_variations(cfg, variations)
                click.echo(f"\nGenerated {len(results)} variations for {cfg.name}")
                for i, result in enumerate(results):
                    if result["success"]:
                        click.echo(f"  Variation {i+1}: {result['output_path']}")
                    else:
                        click.echo(f"  Variation {i+1}: Failed - {result['error']}")
            else:
                result = generator.generate_single_floorplan(cfg, output, custom_prompt)
                if result["success"]:
                    click.echo(f"\nFloor plan generated successfully: {result['output_path']}")
                else:
                    click.echo(f"\nGeneration failed: {result['error']}")
    
    except Exception as e:
        click.echo(f"Error: {str(e)}")
        raise click.Abort()


if __name__ == "__main__":
    main()
