#!/usr/bin/env python3
"""
Text + Image to image floor plan generation script.

This script generates interior floor plan layouts from text prompts and building boundary images
using Alibaba's AI models. It's designed for creating room layouts within existing building outlines.
"""

import click
import json
import time
from pathlib import Path
from typing import Optional, Dict, Any, List
from datetime import datetime
import logging
from PIL import Image

from .config import (
    get_config, list_available_configs, get_api_settings, get_generation_settings,
    FloorPlanConfig, APISettings, GenerationSettings
)
from .api_client import AlibabaAPIClient, AlibabaAPIError
from .prompt_engineering import FloorPlanPromptBuilder, create_prompt_for_config


class InteriorLayoutGenerator:
    """Main class for text+image-to-image interior layout generation."""
    
    def __init__(self, api_settings: Optional[APISettings] = None,
                 generation_settings: Optional[GenerationSettings] = None):
        """Initialize the interior layout generator."""
        self.api_settings = api_settings or get_api_settings()
        self.generation_settings = generation_settings or get_generation_settings()
        self.api_client = AlibabaAPIClient(self.api_settings, self.generation_settings)
        self.prompt_builder = FloorPlanPromptBuilder()
        
        # Set up directories
        self.output_dir = Path(self.generation_settings.output_dir)
        self.sample_images_dir = Path(self.generation_settings.sample_images_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.sample_images_dir.mkdir(parents=True, exist_ok=True)
        
        # Set up logging
        self.logger = logging.getLogger(__name__)
    
    def validate_input_image(self, image_path: str) -> bool:
        """Validate that the input image is suitable for processing."""
        try:
            image_path = Path(image_path)
            if not image_path.exists():
                self.logger.error(f"Input image not found: {image_path}")
                return False
            
            with Image.open(image_path) as img:
                # Check image format
                if img.format not in ['PNG', 'JPEG', 'JPG']:
                    self.logger.error(f"Unsupported image format: {img.format}")
                    return False
                
                # Check image size
                width, height = img.size
                if width < 512 or height < 512:
                    self.logger.warning(f"Image resolution is low: {width}x{height}")
                
                # Check if image is too large
                if width > 2048 or height > 2048:
                    self.logger.warning(f"Image resolution is very high: {width}x{height}")
                
                self.logger.info(f"Input image validated: {width}x{height}, {img.format}")
                return True
                
        except Exception as e:
            self.logger.error(f"Image validation failed: {str(e)}")
            return False
    
    def generate_interior_layout(self, config: FloorPlanConfig, input_image_path: str,
                               output_filename: Optional[str] = None,
                               custom_prompt: Optional[str] = None,
                               strength: float = 0.8) -> Dict[str, Any]:
        """Generate interior layout from configuration and boundary image."""
        self.logger.info(f"Starting interior layout generation for: {config.name}")
        
        # Validate input image
        if not self.validate_input_image(input_image_path):
            raise ValueError(f"Invalid input image: {input_image_path}")
        
        # Build prompt
        if custom_prompt:
            prompt = custom_prompt
        else:
            prompt = self.prompt_builder.build_image_to_image_prompt(config)
        
        negative_prompt = self.prompt_builder.build_negative_prompt()
        
        self.logger.info(f"Generated prompt: {prompt[:200]}...")
        
        try:
            # Make API request
            response = self.api_client.image_to_image(
                prompt=prompt,
                input_image_path=input_image_path,
                negative_prompt=negative_prompt,
                strength=strength,
                image_size=self.generation_settings.default_image_size,
                steps=self.generation_settings.default_steps,
                guidance_scale=self.generation_settings.default_guidance_scale
            )
            
            # Handle async response
            if "task_id" in response.get("output", {}):
                task_id = response["output"]["task_id"]
                self.logger.info(f"Async task created: {task_id}")
                
                # Wait for completion
                final_response = self.api_client.wait_for_completion(task_id)
                image_url = final_response["output"]["results"][0]["url"]
            else:
                # Synchronous response
                image_url = response["output"]["results"][0]["url"]
            
            # Download image
            if not output_filename:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                safe_name = config.name.lower().replace(" ", "_").replace("-", "_")
                input_name = Path(input_image_path).stem
                output_filename = f"{safe_name}_interior_{input_name}_{timestamp}.png"
            
            output_path = self.output_dir / output_filename
            downloaded_path = self.api_client.download_image(image_url, str(output_path))
            
            # Validate generated image
            if not self.api_client.validate_image(downloaded_path):
                raise AlibabaAPIError("Generated image failed validation")
            
            result = {
                "success": True,
                "config_name": config.name,
                "input_image_path": input_image_path,
                "prompt": prompt,
                "negative_prompt": negative_prompt,
                "output_path": downloaded_path,
                "image_url": image_url,
                "generation_time": datetime.now().isoformat(),
                "settings": {
                    "strength": strength,
                    "image_size": self.generation_settings.default_image_size,
                    "steps": self.generation_settings.default_steps,
                    "guidance_scale": self.generation_settings.default_guidance_scale
                }
            }
            
            self.logger.info(f"Successfully generated interior layout: {downloaded_path}")
            return result
            
        except Exception as e:
            error_result = {
                "success": False,
                "config_name": config.name,
                "input_image_path": input_image_path,
                "prompt": prompt,
                "error": str(e),
                "generation_time": datetime.now().isoformat()
            }
            self.logger.error(f"Generation failed: {str(e)}")
            return error_result
    
    def generate_multiple_strengths(self, config: FloorPlanConfig, input_image_path: str,
                                  strengths: List[float] = [0.6, 0.8, 1.0]) -> List[Dict[str, Any]]:
        """Generate interior layouts with different strength values for comparison."""
        self.logger.info(f"Generating layouts with {len(strengths)} different strengths")
        
        results = []
        for i, strength in enumerate(strengths):
            self.logger.info(f"Generating with strength {strength} ({i+1}/{len(strengths)})")
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            safe_name = config.name.lower().replace(" ", "_").replace("-", "_")
            input_name = Path(input_image_path).stem
            output_filename = f"{safe_name}_interior_{input_name}_strength{strength}_{timestamp}.png"
            
            result = self.generate_interior_layout(
                config=config,
                input_image_path=input_image_path,
                output_filename=output_filename,
                strength=strength
            )
            
            result["strength_info"] = {
                "strength_value": strength,
                "strength_index": i,
                "description": self._get_strength_description(strength)
            }
            
            results.append(result)
            
            # Small delay between generations
            if i < len(strengths) - 1:
                time.sleep(2)
        
        return results
    
    def batch_generate_interiors(self, config_names: List[str], input_images: List[str],
                               test_strengths: bool = False) -> Dict[str, Any]:
        """Generate interior layouts for multiple configurations and input images."""
        self.logger.info(f"Starting batch interior generation")
        
        batch_results = {
            "batch_id": datetime.now().strftime("%Y%m%d_%H%M%S"),
            "total_configs": len(config_names),
            "total_images": len(input_images),
            "results": [],
            "summary": {"successful": 0, "failed": 0}
        }
        
        for config_name in config_names:
            try:
                config = get_config(config_name)
                self.logger.info(f"Processing configuration: {config_name}")
                
                for input_image in input_images:
                    self.logger.info(f"Processing image: {input_image}")
                    
                    if test_strengths:
                        config_results = self.generate_multiple_strengths(config, input_image)
                    else:
                        config_results = [self.generate_interior_layout(config, input_image)]
                    
                    batch_results["results"].extend(config_results)
                    
                    # Update summary
                    for result in config_results:
                        if result["success"]:
                            batch_results["summary"]["successful"] += 1
                        else:
                            batch_results["summary"]["failed"] += 1
                
            except Exception as e:
                error_result = {
                    "success": False,
                    "config_name": config_name,
                    "error": f"Configuration error: {str(e)}",
                    "generation_time": datetime.now().isoformat()
                }
                batch_results["results"].append(error_result)
                batch_results["summary"]["failed"] += 1
                self.logger.error(f"Failed to process {config_name}: {str(e)}")
        
        # Save batch results
        results_file = self.output_dir / f"interior_batch_results_{batch_results['batch_id']}.json"
        with open(results_file, 'w') as f:
            json.dump(batch_results, f, indent=2)
        
        self.logger.info(f"Batch generation completed. Results saved to: {results_file}")
        return batch_results
    
    def list_sample_images(self) -> List[str]:
        """List available sample images."""
        image_extensions = ['.png', '.jpg', '.jpeg']
        sample_images = []
        
        for ext in image_extensions:
            sample_images.extend(self.sample_images_dir.glob(f"*{ext}"))
            sample_images.extend(self.sample_images_dir.glob(f"*{ext.upper()}"))
        
        return [str(img) for img in sample_images]
    
    def _get_strength_description(self, strength: float) -> str:
        """Get description for different strength values."""
        if strength <= 0.5:
            return "Low strength - preserves most of original image structure"
        elif strength <= 0.7:
            return "Medium-low strength - moderate changes to original"
        elif strength <= 0.9:
            return "Medium-high strength - significant modifications"
        else:
            return "High strength - major transformation of original"


@click.command()
@click.option('--config', '-c', help='Floor plan configuration name')
@click.option('--input-image', '-i', help='Path to input boundary image')
@click.option('--list-configs', is_flag=True, help='List available configurations')
@click.option('--list-samples', is_flag=True, help='List available sample images')
@click.option('--strength', '-s', default=0.8, help='Transformation strength (0.0-1.0)')
@click.option('--test-strengths', is_flag=True, help='Test multiple strength values')
@click.option('--batch-configs', help='Comma-separated list of config names')
@click.option('--batch-images', help='Comma-separated list of image paths')
@click.option('--output', '-o', help='Output filename')
@click.option('--custom-prompt', help='Custom prompt instead of generated one')
@click.option('--dry-run', is_flag=True, help='Show prompts without generating images')
def main(config: Optional[str], input_image: Optional[str], list_configs: bool,
         list_samples: bool, strength: float, test_strengths: bool,
         batch_configs: Optional[str], batch_images: Optional[str],
         output: Optional[str], custom_prompt: Optional[str], dry_run: bool):
    """
    Generate interior floor plan layouts from text descriptions and boundary images.
    
    Examples:
        text-image-to-floorplan --list-configs
        text-image-to-floorplan --list-samples
        text-image-to-floorplan --config 2br_apartment --input-image boundary.png
        text-image-to-floorplan --config 2br_apartment --input-image boundary.png --test-strengths
    """
    
    if list_configs:
        available_configs = list_available_configs()
        click.echo("Available floor plan configurations:")
        for config_name in available_configs:
            try:
                cfg = get_config(config_name)
                click.echo(f"  {config_name}: {cfg.name} ({cfg.total_area_sqm}m²)")
            except Exception as e:
                click.echo(f"  {config_name}: Error loading config - {e}")
        return
    
    if list_samples:
        from .config import get_generation_settings
        gen_settings = get_generation_settings()
        sample_dir = Path(gen_settings.sample_images_dir)

        # List sample images without initializing full generator
        image_extensions = ['.png', '.jpg', '.jpeg']
        sample_images = []
        for ext in image_extensions:
            sample_images.extend(sample_dir.glob(f"*{ext}"))
            sample_images.extend(sample_dir.glob(f"*{ext.upper()}"))

        click.echo("Available sample images:")
        if sample_images:
            for img_path in sample_images:
                click.echo(f"  {img_path}")
        else:
            click.echo(f"  No sample images found in {sample_dir}")
            click.echo(f"  Add boundary images to this directory for testing")
        return
    
    if not config and not batch_configs:
        click.echo("Error: Must specify either --config or --batch-configs")
        return
    
    if not input_image and not batch_images:
        click.echo("Error: Must specify either --input-image or --batch-images")
        return
    
    try:
        if dry_run:
            # Show prompts without generating (no API client needed)
            if config:
                cfg = get_config(config)
                builder = FloorPlanPromptBuilder()

                if custom_prompt:
                    prompt = custom_prompt
                else:
                    prompt = builder.build_image_to_image_prompt(cfg)
                negative_prompt = builder.build_negative_prompt()

                click.echo(f"\nConfiguration: {cfg.name}")
                click.echo(f"Input Image: {input_image}")
                click.echo(f"Prompt: {prompt}")
                click.echo(f"Negative Prompt: {negative_prompt}")
                click.echo(f"Strength: {strength}")
            return

        generator = InteriorLayoutGenerator()
        
        if batch_configs and batch_images:
            # Batch generation
            config_names = [name.strip() for name in batch_configs.split(',')]
            image_paths = [path.strip() for path in batch_images.split(',')]
            
            results = generator.batch_generate_interiors(
                config_names, image_paths, test_strengths=test_strengths
            )
            
            click.echo(f"\nBatch interior generation completed:")
            click.echo(f"  Successful: {results['summary']['successful']}")
            click.echo(f"  Failed: {results['summary']['failed']}")
            
        else:
            # Single generation
            cfg = get_config(config)
            
            if test_strengths:
                results = generator.generate_multiple_strengths(cfg, input_image)
                click.echo(f"\nGenerated {len(results)} strength variations for {cfg.name}")
                for result in results:
                    if result["success"]:
                        strength_val = result["strength_info"]["strength_value"]
                        click.echo(f"  Strength {strength_val}: {result['output_path']}")
                    else:
                        click.echo(f"  Failed: {result['error']}")
            else:
                result = generator.generate_interior_layout(
                    cfg, input_image, output, custom_prompt, strength
                )
                if result["success"]:
                    click.echo(f"\nInterior layout generated: {result['output_path']}")
                else:
                    click.echo(f"\nGeneration failed: {result['error']}")
    
    except Exception as e:
        click.echo(f"Error: {str(e)}")
        raise click.Abort()


if __name__ == "__main__":
    main()
