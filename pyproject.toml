[project]
name = "floorplan-invest"
version = "0.1.0"
description = "AI-powered floor plan generation testing framework using Aliba<PERSON>'s models"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "requests>=2.31.0",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    "pillow>=10.0.0",
    "python-dotenv>=1.0.0",
    "click>=8.1.0",
    "rich>=13.0.0",
    "pathlib>=1.0.1",
    "typing-extensions>=4.8.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-cov>=4.1.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
]

[project.scripts]
text-to-floorplan = "floorplan_invest.text_to_floorplan:main"
text-image-to-floorplan = "floorplan_invest.text_image_to_floorplan:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.black]
line-length = 100
target-version = ['py312']

[tool.isort]
profile = "black"
line_length = 100

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
